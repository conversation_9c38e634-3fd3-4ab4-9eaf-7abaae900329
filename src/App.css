/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
  'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
  sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  min-height: 100vh;
  padding: 20px;
  position: relative;
}

/* Animated background elements */
body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Main app container */
.App {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Age Prediction Section */
.App > input,
.App > button:first-of-type,
.userDetails {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
          0 20px 40px rgba(0, 0, 0, 0.1),
          0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Create age prediction container */
.App > input,
.App > button:first-of-type,
.userDetails {
  position: relative;
}

.App > input::before,
.App > button:first-of-type::before {
  content: '🔮 Age Oracle';
  position: absolute;
  top: -35px;
  left: 20px;
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(93, 92, 222, 0.8);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* Input field styling - Age Prediction */
.App input {
  width: 100%;
  padding: 20px 24px;
  font-size: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  outline: none;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
}

.App input:focus {
  border-color: #5D5CDE;
  box-shadow:
          0 8px 25px rgba(93, 92, 222, 0.2),
          0 0 0 3px rgba(93, 92, 222, 0.1);
  transform: translateY(-2px);
}

.App input::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

/* Age prediction button */
.App > button:first-of-type {
  width: 100%;
  padding: 18px 32px;
  background: linear-gradient(135deg, #5D5CDE 0%, #8B7CF8 100%);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.App > button:first-of-type::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.App > button:first-of-type:hover::after {
  left: 100%;
}

.App > button:first-of-type:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(93, 92, 222, 0.4);
}

/* User details styling */
.userDetails {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(226, 232, 240, 0.95) 100%);
  border-radius: 20px;
  padding: 30px;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 15px;
}

.userDetails::before {
  content: '👤';
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 20px;
  opacity: 0.4;
}

.userDetails h1 {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 10px;
  text-transform: capitalize;
  background: linear-gradient(135deg, #5D5CDE, #8B7CF8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  position: relative;
}

.userDetails h1::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(135deg, #5D5CDE, #8B7CF8);
  border-radius: 2px;
}

.userDetails h2 {
  font-size: 20px;
  font-weight: 600;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.userDetails h2:first-of-type::before {
  content: '🎂';
  font-size: 18px;
}

.userDetails h2:first-of-type::after {
  content: ' years old';
  font-size: 16px;
  color: #718096;
  font-weight: 400;
}

.userDetails h2:last-of-type::before {
  content: '📊';
  font-size: 18px;
}

.userDetails h2:last-of-type::after {
  content: ' data points';
  font-size: 16px;
  color: #718096;
  font-weight: 400;
}

/* Divider styling */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  margin: 20px 0;
}

/* Excuse Section Container */
.excuse {
  background: linear-gradient(135deg, rgba(255, 154, 158, 0.95) 0%, rgba(250, 208, 196, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 35px;
  box-shadow:
          0 25px 50px rgba(255, 154, 158, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  text-align: center;
}

/* Excuse section decorative elements */
.excuse::before {
  content: '🎭✨';
  position: absolute;
  top: 20px;
  right: 25px;
  font-size: 24px;
  opacity: 0.7;
}

.excuse::after {
  content: '💬';
  position: absolute;
  bottom: 20px;
  left: 25px;
  font-size: 20px;
  opacity: 0.6;
}

/* Excuse section title */
.excuse h2 {
  font-size: 28px;
  font-weight: 700;
  color: #742a2a;
  margin-bottom: 25px;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  display: inline-block;
}

.excuse h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #e53e3e, #fc8181);
  border-radius: 2px;
}

/* Excuse buttons container */
.excuse {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.excuse button {
  background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 8px 20px rgba(229, 62, 62, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  margin: 0 auto;
}

.excuse button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.excuse button:hover::before {
  left: 100%;
}

.excuse button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(229, 62, 62, 0.4);
  background: linear-gradient(135deg, #c53030 0%, #f56565 100%);
}

.excuse button:active {
  transform: translateY(-1px);
}

/* Add icons to excuse buttons */
.excuse button[value="party"]::after {
  content: ' 🎉';
}

.excuse button[value="family"]::after {
  content: ' 👨‍👩‍👧‍👦';
}

.excuse button[value="office"]::after {
  content: ' 💼';
}

/* Custom Modal for excuses */
.excuse-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease-out;
}

.excuse-modal-content {
  background: white;
  padding: 30px;
  border-radius: 20px;
  max-width: 500px;
  width: 90%;
  text-align: center;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
  position: relative;
}

.excuse-modal-content::before {
  content: '💡';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 30px;
  background: white;
  padding: 10px;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.excuse-modal h3 {
  color: #2d3748;
  font-size: 22px;
  margin-bottom: 15px;
  font-weight: 700;
}

.excuse-modal p {
  color: #4a5568;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  font-style: italic;
  background: #f7fafc;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #e53e3e;
}

.excuse-modal button {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.excuse-modal button:hover {
  background: #c53030;
  transform: translateY(-2px);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  body {
    padding: 15px;
  }

  .App {
    gap: 25px;
  }

  .App input {
    font-size: 16px;
    padding: 18px 20px;
  }

  .App > button:first-of-type {
    font-size: 16px;
    padding: 16px 28px;
  }

  .userDetails {
    padding: 25px 20px;
    min-height: 160px;
  }

  .userDetails h1 {
    font-size: 28px;
  }

  .userDetails h2 {
    font-size: 18px;
    padding: 10px 16px;
  }

  .excuse {
    padding: 30px 25px;
  }

  .excuse h2 {
    font-size: 24px;
  }

  .excuse button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .App input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 16px 18px;
  }

  .excuse {
    padding: 25px 20px;
  }

  .excuse h2 {
    font-size: 20px;
  }

  .excuse button {
    font-size: 14px;
    padding: 14px 24px;
  }

  .excuse-modal-content {
    padding: 25px 20px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
  }

  .App > input,
  .App > button:first-of-type,
  .userDetails {
    background: rgba(45, 55, 72, 0.95);
    color: #e2e8f0;
  }

  .App input {
    background: rgba(26, 32, 44, 0.9);
    border-color: rgba(74, 85, 104, 0.5);
    color: #e2e8f0;
  }

  .userDetails {
    background: linear-gradient(135deg, rgba(74, 85, 104, 0.95) 0%, rgba(45, 55, 72, 0.95) 100%);
  }

  .userDetails h2 {
    background: rgba(26, 32, 44, 0.8);
    color: #e2e8f0;
  }

  .excuse {
    background: linear-gradient(135deg, rgba(124, 45, 18, 0.95) 0%, rgba(154, 52, 18, 0.95) 100%);
  }

  .excuse h2 {
    color: #fed7d7;
  }

  .excuse-modal-content {
    background: #2d3748;
    color: #e2e8f0;
  }

  .excuse-modal h3 {
    color: #e2e8f0;
  }

  .excuse-modal p {
    background: #4a5568;
    color: #e2e8f0;
  }
}

/* Animation for section entries */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.userDetails,
.excuse {
  animation: slideInUp 0.6s ease-out;
}

/* Excuse paragraph styling */
.excuse p {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px 25px;
  margin-top: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #742a2a;
  line-height: 1.6;
  text-align: center;
  box-shadow:
          0 8px 25px rgba(116, 42, 42, 0.1),
          inset 0 1px 3px rgba(255, 255, 255, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.excuse p::before {
  content: '💬';
  position: absolute;
  top: -8px;
  left: 20px;
  font-size: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.excuse p:hover {
  transform: translateY(-2px);
  box-shadow:
          0 12px 30px rgba(116, 42, 42, 0.15),
          inset 0 1px 3px rgba(255, 255, 255, 0.6);
}

.excuse p:empty {
  background: rgba(255, 255, 255, 0.6);
  color: #a0aec0;
  font-style: italic;
  border-style: dashed;
}

.excuse p:empty::before {
  display: none;
}

.excuse p:empty::after {
  content: 'Select a category to see your excuse type here';
  color: #a0aec0;
}

/* Dark mode support for excuse paragraph */
@media (prefers-color-scheme: dark) {
  .excuse p {
    background: rgba(26, 32, 44, 0.9);
    color: #fed7d7;
    border-color: rgba(74, 85, 104, 0.5);
  }

  .excuse p::before {
    background: rgba(45, 55, 72, 0.9);
  }

  .excuse p:empty {
    background: rgba(26, 32, 44, 0.6);
    color: #a0aec0;
  }
}