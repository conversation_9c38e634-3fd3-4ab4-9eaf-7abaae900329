import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

function Counter() {
    const [counter, setCounter] = useState(0);
    const [stepValue, setStepValue] = useState(1);
    const [error, setError] = useState(null);
    const navigate = useNavigate();

    useEffect(() => {
        async function saveCounter() {
            try {
                await axios.post('https://your-lambda-api/submit', { counter });
                console.log('Counter saved');
            } catch (err) {
                setError('Failed to save counter');
            }
        }
        if (counter !== 0) saveCounter();
    }, [counter]);

    const increment = () => {
        const step = parseInt(document.getElementById('stepValue').value) || stepValue;
        setCounter(counter + step);
        setStepValue(step);
    };

    const decrement = () => {
        const step = parseInt(document.getElementById('stepValue').value) || stepValue;
        setCounter(counter - step);
        setStepValue(step);
    };

    return (
        <div className="counter-container">
            <h1>Counter: {counter}</h1>
            {error && <p className="error">{error}</p>}
            <div className="button-container">
                <button onClick={decrement}>Decrease Value</button>
                <button onClick={increment}>Increase Value</button>
                <button onClick={() => navigate('/')}>Go to Home</button>
            </div>
            <input
                id="stepValue"
                type="number"
                placeholder="Step (default: 1)"
                onChange={(e) => setStepValue(parseInt(e.target.value) || 1)}
            />
        </div>
    );
}

export default Counter;