import {BrowserRouter, Routes, Route} from 'react-router-dom';
import { Home} from './pages/Home';
import { Profile } from './pages/Profile';
import { Contact } from './pages/Contact';
import './App.css';
import {Navbar} from "./pages/Navbar";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";

function App() {

        const queryClient = new QueryClient();

    return (


        <QueryClientProvider  client={queryClient}>
        <BrowserRouter>
            <Navbar />
            <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/profile" element={<Profile/>} />
                <Route path="/contact" element={<Contact />} />
                <Route path="*" element={<h1>404 Not Found</h1>} />
            </Routes>
        </BrowserRouter>
        </QueryClientProvider>

    );
}

export default App;