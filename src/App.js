import React from 'react';
import { BrowserRouter, Routes, Route, NavLink } from 'react-router-dom';
import Counter from './Counter';
import Home from './Home';
import './App.css';

function App() {
    return (
        <BrowserRouter>
            <div className="App">
                <nav className="nav-bar">
                    <NavLink to="/" className={({ isActive }) => (isActive ? 'active' : '')}>
                        Home
                    </NavLink>
                    <NavLink to="/counter" className={({ isActive }) => (isActive ? 'active' : '')}>
                        Counter
                    </NavLink>
                </nav>
                <Routes>
                    <Route path="/" element={<Home />} />
                    <Route path="/counter" element={<Counter />} />
                </Routes>
            </div>
        </BrowserRouter>
    );
}

export default App;